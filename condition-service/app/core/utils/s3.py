import os

def get_file(bucket: str, key: str) -> str:
    # Prepare local path
    local_path = os.path.join("/tmp", os.path.basename(key))

    try:
        # Download file from S3
        with open(local_path, "wb") as f:
            s3.download_fileobj(bucket, key, f)

        # Additional logging for models
        if file_type == "Models":
            print("local image path", local_path)

        return local_path

    except Exception as e:
        print(f"Error in _download_file_from_s3: {e}")
        return {"status": "Failure", "message": str(e)}
